import Image from 'next/image'
import Link from 'next/link'
import { formatDistanceToNow } from 'date-fns'
import { hi } from 'date-fns/locale'

interface Article {
  id: string
  title: string
  excerpt: string
  image_url?: string
  published_at: string
  category: string
  slug: string
}

interface Category {
  id: string
  name: string
  slug: string
}

interface TraditionalBottomSectionProps {
  articles: Article[]
  categories: Category[]
}

export function TraditionalBottomSection({ articles, categories }: TraditionalBottomSectionProps) {
  return (
    <div className="bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4">
        {/* Photo Gallery Section */}
        <section className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900 border-b-4 border-red-600 pb-2">
              Photo Gallery
            </h2>
            <Link href="/photos" className="text-red-600 hover:text-red-800 font-medium">
              View All Photos →
            </Link>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {articles.slice(0, 6).map((article) => (
              <Link key={article.id} href={`/article/${article.slug}`}>
                <div className="group cursor-pointer">
                  <div className="relative h-32 overflow-hidden rounded-lg">
                    {article.image_url ? (
                      <Image
                        src={article.image_url}
                        alt={article.title}
                        fill
                        className="object-cover group-hover:scale-110 transition-transform duration-300"
                      />
                    ) : (
                      <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                        <span className="text-xs text-gray-500">IMG</span>
                      </div>
                    )}
                    <div className="absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-colors" />
                  </div>
                  <h3 className="text-sm font-medium text-gray-900 mt-2 line-clamp-2 group-hover:text-red-600 transition-colors">
                    {article.title}
                  </h3>
                </div>
              </Link>
            ))}
          </div>
        </section>

        {/* Multiple Category Sections */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Sports Section */}
          <section>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-gray-900 border-b-4 border-orange-600 pb-2">
                खेल
              </h3>
              <Link href="/category/sports" className="text-orange-600 hover:text-orange-800 font-medium text-sm">
                और देखें →
              </Link>
            </div>
            <div className="space-y-3">
              {articles.slice(0, 4).map((article) => (
                <Link key={article.id} href={`/news/${article.slug}`}>
                  <div className="flex gap-3 group cursor-pointer border-b border-gray-200 pb-3">
                    <div className="flex-shrink-0 w-16 h-12 relative overflow-hidden rounded">
                      {article.image_url ? (
                        <Image
                          src={article.image_url}
                          alt={article.title}
                          fill
                          className="object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                      ) : (
                        <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                          <span className="text-xs text-gray-500">IMG</span>
                        </div>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-semibold text-gray-900 line-clamp-2 group-hover:text-orange-600 transition-colors">
                        {article.title}
                      </h4>
                      <p className="text-xs text-gray-500 mt-1">
                        {formatDistanceToNow(new Date(article.published_at), { 
                          addSuffix: true, 
                          locale: hi 
                        })}
                      </p>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </section>

          {/* Entertainment Section */}
          <section>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-gray-900 border-b-4 border-pink-600 pb-2">
                मनोरंजन
              </h3>
              <Link href="/category/entertainment" className="text-pink-600 hover:text-pink-800 font-medium text-sm">
                और देखें →
              </Link>
            </div>
            <div className="space-y-3">
              {articles.slice(4, 8).map((article) => (
                <Link key={article.id} href={`/news/${article.slug}`}>
                  <div className="flex gap-3 group cursor-pointer border-b border-gray-200 pb-3">
                    <div className="flex-shrink-0 w-16 h-12 relative overflow-hidden rounded">
                      {article.image_url ? (
                        <Image
                          src={article.image_url}
                          alt={article.title}
                          fill
                          className="object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                      ) : (
                        <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                          <span className="text-xs text-gray-500">IMG</span>
                        </div>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-semibold text-gray-900 line-clamp-2 group-hover:text-pink-600 transition-colors">
                        {article.title}
                      </h4>
                      <p className="text-xs text-gray-500 mt-1">
                        {formatDistanceToNow(new Date(article.published_at), { 
                          addSuffix: true, 
                          locale: hi 
                        })}
                      </p>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </section>

          {/* Technology Section */}
          <section>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-gray-900 border-b-4 border-indigo-600 pb-2">
                तकनीक
              </h3>
              <Link href="/category/technology" className="text-indigo-600 hover:text-indigo-800 font-medium text-sm">
                और देखें →
              </Link>
            </div>
            <div className="space-y-3">
              {articles.slice(8, 12).map((article) => (
                <Link key={article.id} href={`/news/${article.slug}`}>
                  <div className="flex gap-3 group cursor-pointer border-b border-gray-200 pb-3">
                    <div className="flex-shrink-0 w-16 h-12 relative overflow-hidden rounded">
                      {article.image_url ? (
                        <Image
                          src={article.image_url}
                          alt={article.title}
                          fill
                          className="object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                      ) : (
                        <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                          <span className="text-xs text-gray-500">IMG</span>
                        </div>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-semibold text-gray-900 line-clamp-2 group-hover:text-indigo-600 transition-colors">
                        {article.title}
                      </h4>
                      <p className="text-xs text-gray-500 mt-1">
                        {formatDistanceToNow(new Date(article.published_at), { 
                          addSuffix: true, 
                          locale: hi 
                        })}
                      </p>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </section>
        </div>

        {/* Newsletter Subscription */}
        <section className="mt-12 bg-gradient-to-r from-blue-600 to-purple-600 text-white p-8 rounded-lg">
          <div className="text-center">
            <h3 className="text-2xl font-bold mb-4">समाचार अपडेट पाएं</h3>
            <p className="text-blue-100 mb-6">ताज़ा समाचार सीधे अपने ईमेल में पाने के लिए सब्सक्राइब करें</p>
            <div className="max-w-md mx-auto flex gap-3">
              <input
                type="email"
                placeholder="आपका ईमेल पता"
                className="flex-1 px-4 py-3 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-white"
              />
              <button className="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                सब्सक्राइब करें
              </button>
            </div>
          </div>
        </section>
      </div>
    </div>
  )
}
