'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { Badge } from '@/components/ui/badge'
import { db } from '@/lib/database'

interface BreakingNewsItem {
  id: string
  title: string
  slug: string
}

export function BreakingNewsTicker() {
  const [breakingNews, setBreakingNews] = useState<BreakingNewsItem[]>([])
  const [currentIndex, setCurrentIndex] = useState(0)

  useEffect(() => {
    const fetchBreakingNews = async () => {
      try {
        const articles = await db.getPublishedArticles({
          limit: 5,
          isBreaking: true
        })
        setBreakingNews(articles.map(article => ({
          id: article.id,
          title: article.title,
          slug: article.slug
        })))
      } catch (error) {
        console.error('Error fetching breaking news:', error)
      }
    }

    fetchBreakingNews()
  }, [])

  useEffect(() => {
    if (breakingNews.length > 1) {
      const interval = setInterval(() => {
        setCurrentIndex((prev) => (prev + 1) % breakingNews.length)
      }, 5000) // Change every 5 seconds

      return () => clearInterval(interval)
    }
  }, [breakingNews.length])

  if (breakingNews.length === 0) {
    return null
  }

  const currentNews = breakingNews[currentIndex]

  return (
    <div className="bg-red-600 text-white py-2 overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center">
          <Badge variant="secondary" className="bg-white text-red-600 font-bold mr-4 animate-pulse">
            BREAKING:
          </Badge>
          <div className="flex-1 overflow-hidden">
            <div className="animate-marquee whitespace-nowrap">
              <Link
                href={`/article/${currentNews.slug}`}
                className="hover:underline font-medium"
              >
                Breaking: {currentNews.title}
              </Link>
            </div>
          </div>
          {breakingNews.length > 1 && (
            <div className="flex space-x-1 ml-4">
              {breakingNews.map((_, index) => (
                <button
                  key={index}
                  className={`w-2 h-2 rounded-full transition-colors ${
                    index === currentIndex ? 'bg-white' : 'bg-white/50'
                  }`}
                  onClick={() => setCurrentIndex(index)}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
