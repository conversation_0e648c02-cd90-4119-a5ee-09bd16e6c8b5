'use client'

import { useState } from 'react'
import { AdminLayout } from '@/components/admin/admin-layout'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { toast } from 'react-hot-toast'

export default function WebsiteSettings() {
  const [settings, setSettings] = useState({
    // General Settings
    siteTitle: 'The Sach Patra',
    tagline: 'Your trusted source for news',
    siteDescription: 'The Sach Patra - Your trusted source for the latest news on politics, tech, world, sports, and entertainment.',
    maintenanceMode: false,
    
    // Appearance
    siteLogo: '',
    favicon: '',
    primaryColor: '#dc2626', // red-600
    secondaryColor: '#2563eb', // blue-600
    textColor: '#1f2937', // gray-800
    backgroundColor: '#ffffff',
    
    // Content Settings
    articlesPerPage: 10,
    featuredCategories: ['politics', 'tech', 'world', 'sports', 'entertainment'],
    
    // Security Settings
    twoFactorAuth: false,
    captchaOnForms: true,
    autoLogout: true,
    passwordPolicy: 'strong'
  })

  const handleSaveSettings = () => {
    // Here you would save to your database
    toast.success('Settings saved successfully!')
  }

  const handleSaveAppearance = () => {
    toast.success('Appearance settings saved!')
  }

  return (
    <ProtectedRoute requiredRoles={['admin']}>
      <AdminLayout>
        <div className="space-y-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Website Settings</h1>
            <p className="text-gray-600 mt-1">Manage your website configuration and preferences</p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* General Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <span>⚙️</span>
                  <span>General Settings</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="siteTitle">Site Title</Label>
                  <Input
                    id="siteTitle"
                    value={settings.siteTitle}
                    onChange={(e) => setSettings({...settings, siteTitle: e.target.value})}
                  />
                </div>
                
                <div>
                  <Label htmlFor="tagline">Tagline</Label>
                  <Input
                    id="tagline"
                    value={settings.tagline}
                    onChange={(e) => setSettings({...settings, tagline: e.target.value})}
                  />
                </div>
                
                <div>
                  <Label htmlFor="siteDescription">Site Description</Label>
                  <Textarea
                    id="siteDescription"
                    value={settings.siteDescription}
                    onChange={(e) => setSettings({...settings, siteDescription: e.target.value})}
                    rows={3}
                  />
                </div>
                
                <div className="flex items-center space-x-2">
                  <Switch
                    id="maintenanceMode"
                    checked={settings.maintenanceMode}
                    onCheckedChange={(checked) => setSettings({...settings, maintenanceMode: checked})}
                  />
                  <Label htmlFor="maintenanceMode">Maintenance Mode</Label>
                </div>
                
                <Button onClick={handleSaveSettings} className="w-full bg-red-600 hover:bg-red-700">
                  Save Settings
                </Button>
              </CardContent>
            </Card>

            {/* Appearance */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <span>🎨</span>
                  <span>Appearance</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="siteLogo">Site Logo</Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      id="siteLogo"
                      type="file"
                      accept="image/*"
                      className="flex-1"
                    />
                    <Button variant="outline" size="sm">Choose File</Button>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">Recommended size: 200x50px</p>
                </div>
                
                <div>
                  <Label htmlFor="favicon">Favicon</Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      id="favicon"
                      type="file"
                      accept="image/*"
                      className="flex-1"
                    />
                    <Button variant="outline" size="sm">Choose File</Button>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">Recommended size: 32x32px</p>
                </div>
                
                <div>
                  <Label>Color Scheme</Label>
                  <div className="grid grid-cols-2 gap-4 mt-2">
                    <div className="flex items-center space-x-2">
                      <div className="w-8 h-8 bg-red-600 rounded-full border-2 border-gray-300"></div>
                      <span className="text-sm">Primary</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-8 h-8 bg-blue-600 rounded-full border-2 border-gray-300"></div>
                      <span className="text-sm">Secondary</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-8 h-8 bg-gray-800 rounded-full border-2 border-gray-300"></div>
                      <span className="text-sm">Text</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-8 h-8 bg-white rounded-full border-2 border-gray-300"></div>
                      <span className="text-sm">Background</span>
                    </div>
                  </div>
                </div>
                
                <Button onClick={handleSaveAppearance} className="w-full bg-red-600 hover:bg-red-700">
                  Save Appearance
                </Button>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Content Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <span>📝</span>
                  <span>Content Settings</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="articlesPerPage">Articles Per Page</Label>
                  <Select value={settings.articlesPerPage.toString()}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="5">5</SelectItem>
                      <SelectItem value="10">10</SelectItem>
                      <SelectItem value="15">15</SelectItem>
                      <SelectItem value="20">20</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label>Featured Categories</Label>
                  <div className="space-y-2 mt-2">
                    {['Politics', 'Tech', 'World', 'Sports', 'Entertainment'].map((category) => (
                      <div key={category} className="flex items-center space-x-2">
                        <Checkbox 
                          id={category.toLowerCase()}
                          checked={settings.featuredCategories.includes(category.toLowerCase())}
                        />
                        <Label htmlFor={category.toLowerCase()}>{category}</Label>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Security Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <span>🔒</span>
                  <span>Security Settings</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="twoFactorAuth"
                    checked={settings.twoFactorAuth}
                    onCheckedChange={(checked) => setSettings({...settings, twoFactorAuth: checked})}
                  />
                  <Label htmlFor="twoFactorAuth">Two-Factor Authentication</Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Switch
                    id="captchaOnForms"
                    checked={settings.captchaOnForms}
                    onCheckedChange={(checked) => setSettings({...settings, captchaOnForms: checked})}
                  />
                  <Label htmlFor="captchaOnForms">CAPTCHA on Forms</Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Switch
                    id="autoLogout"
                    checked={settings.autoLogout}
                    onCheckedChange={(checked) => setSettings({...settings, autoLogout: checked})}
                  />
                  <Label htmlFor="autoLogout">Auto Logout (30 min inactive)</Label>
                </div>
                
                <div>
                  <Label htmlFor="passwordPolicy">Password Policy</Label>
                  <Select value={settings.passwordPolicy}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="weak">Weak (6+ chars, numbers, letters, special)</SelectItem>
                      <SelectItem value="strong">Strong (8+ chars, numbers, letters, special)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </AdminLayout>
    </ProtectedRoute>
  )
}
