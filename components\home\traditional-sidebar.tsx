import Image from 'next/image'
import Link from 'next/link'
import { formatDistanceToNow } from 'date-fns'
import { enUS } from 'date-fns/locale'

interface Article {
  id: string
  title: string
  excerpt: string
  image_url?: string
  published_at: string
  category: string
  slug: string
}

interface Category {
  id: string
  name: string
  slug: string
}

interface TraditionalSidebarProps {
  trendingArticles: Article[]
  categories: Category[]
}

export function TraditionalSidebar({ trendingArticles, categories }: TraditionalSidebarProps) {
  return (
    <div className="space-y-6">
      {/* Trending News */}
      <div className="bg-red-50 border-l-4 border-red-600 p-4 rounded-r-lg">
        <h3 className="text-lg font-bold text-red-600 mb-4 flex items-center">
          <span className="mr-2">🔥</span>
          Trending News
        </h3>
        <div className="space-y-3">
          {trendingArticles.slice(0, 5).map((article, index) => (
            <Link key={article.id} href={`/news/${article.slug}`}>
              <div className="flex gap-3 group cursor-pointer">
                <div className="flex-shrink-0 w-12 h-12 bg-red-600 text-white rounded-full flex items-center justify-center font-bold text-sm">
                  {index + 1}
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className="text-sm font-semibold text-gray-900 line-clamp-2 group-hover:text-red-600 transition-colors">
                    {article.title}
                  </h4>
                  <p className="text-xs text-gray-500 mt-1">
                    {formatDistanceToNow(new Date(article.published_at), { 
                      addSuffix: true, 
                      locale: hi 
                    })}
                  </p>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>

      {/* Categories Quick Access */}
      <div className="bg-blue-50 border-l-4 border-blue-600 p-4 rounded-r-lg">
        <h3 className="text-lg font-bold text-blue-600 mb-4">
          समाचार श्रेणियां
        </h3>
        <div className="grid grid-cols-2 gap-2">
          {categories.map((category) => (
            <Link key={category.id} href={`/category/${category.slug}`}>
              <div className="bg-white border border-gray-200 rounded-lg p-3 text-center hover:bg-blue-50 hover:border-blue-300 transition-colors group">
                <span className="text-sm font-medium text-gray-700 group-hover:text-blue-600">
                  {category.name}
                </span>
              </div>
            </Link>
          ))}
        </div>
      </div>

      {/* Most Read */}
      <div className="bg-green-50 border-l-4 border-green-600 p-4 rounded-r-lg">
        <h3 className="text-lg font-bold text-green-600 mb-4 flex items-center">
          <span className="mr-2">📖</span>
          सबसे ज्यादा पढ़े गए
        </h3>
        <div className="space-y-3">
          {trendingArticles.slice(5, 8).map((article, index) => (
            <Link key={article.id} href={`/news/${article.slug}`}>
              <div className="flex gap-3 group cursor-pointer border-b border-green-200 pb-3 last:border-b-0">
                <div className="flex-shrink-0 w-16 h-12 relative overflow-hidden rounded">
                  {article.image_url ? (
                    <Image
                      src={article.image_url}
                      alt={article.title}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  ) : (
                    <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                      <span className="text-xs text-gray-500">IMG</span>
                    </div>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className="text-sm font-semibold text-gray-900 line-clamp-2 group-hover:text-green-600 transition-colors">
                    {article.title}
                  </h4>
                  <p className="text-xs text-gray-500 mt-1">
                    {formatDistanceToNow(new Date(article.published_at), {
                      addSuffix: true,
                      locale: enUS
                    })}
                  </p>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>

      {/* Advertisement Placeholder */}
      <div className="bg-gray-100 border-2 border-dashed border-gray-300 p-8 text-center rounded-lg">
        <div className="text-gray-500 mb-2">
          <svg className="w-12 h-12 mx-auto" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
          </svg>
        </div>
        <p className="text-sm text-gray-600 font-medium">विज्ञापन स्थान</p>
        <p className="text-xs text-gray-500">Advertisement Space</p>
      </div>

      {/* Social Media Links */}
      <div className="bg-gray-50 border border-gray-200 p-4 rounded-lg">
        <h3 className="text-lg font-bold text-gray-900 mb-4">
          हमसे जुड़ें
        </h3>
        <div className="grid grid-cols-2 gap-3">
          <a href="#" className="flex items-center justify-center bg-blue-600 text-white p-3 rounded-lg hover:bg-blue-700 transition-colors">
            <span className="text-sm font-medium">Facebook</span>
          </a>
          <a href="#" className="flex items-center justify-center bg-blue-400 text-white p-3 rounded-lg hover:bg-blue-500 transition-colors">
            <span className="text-sm font-medium">Twitter</span>
          </a>
          <a href="#" className="flex items-center justify-center bg-red-600 text-white p-3 rounded-lg hover:bg-red-700 transition-colors">
            <span className="text-sm font-medium">YouTube</span>
          </a>
          <a href="#" className="flex items-center justify-center bg-pink-600 text-white p-3 rounded-lg hover:bg-pink-700 transition-colors">
            <span className="text-sm font-medium">Instagram</span>
          </a>
        </div>
      </div>

      {/* Weather Widget Placeholder */}
      <div className="bg-gradient-to-br from-blue-400 to-blue-600 text-white p-4 rounded-lg">
        <h3 className="text-lg font-bold mb-3 flex items-center">
          <span className="mr-2">🌤️</span>
          Weather
        </h3>
        <div className="text-center">
          <div className="text-3xl font-bold mb-1">28°C</div>
          <div className="text-sm opacity-90 mb-2">New Delhi</div>
          <div className="text-xs opacity-80">Partly Cloudy</div>
        </div>
      </div>
    </div>
  )
}
