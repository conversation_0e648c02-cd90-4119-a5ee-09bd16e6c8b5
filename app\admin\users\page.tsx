'use client'

import { useState, useEffect } from 'react'
import { AdminLayout } from '@/components/admin/admin-layout'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  Search,
  Filter,
  UserPlus,
  Edit,
  Trash2,
  MoreHorizontal
} from 'lucide-react'
import { toast } from 'react-hot-toast'

interface User {
  id: string
  name: string
  email: string
  role: 'Admin' | 'Editor' | 'Writer' | 'Subscriber'
  status: 'Active' | 'Inactive'
  lastLogin: string
}

interface Permission {
  name: string
  admin: boolean
  editor: boolean
  writer: boolean
  subscriber: boolean
}

const mockUsers: User[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Admin',
    status: 'Active',
    lastLogin: '2023-07-01'
  },
  {
    id: '2',
    name: '<PERSON> Smith',
    email: '<EMAIL>',
    role: 'Editor',
    status: 'Active',
    lastLogin: '2023-06-30'
  },
  {
    id: '3',
    name: 'Michael Brown',
    email: '<EMAIL>',
    role: 'Writer',
    status: 'Inactive',
    lastLogin: '2023-06-25'
  },
  {
    id: '4',
    name: 'Sarah Wilson',
    email: '<EMAIL>',
    role: 'Subscriber',
    status: 'Active',
    lastLogin: '2023-07-02'
  }
]

const permissions: Permission[] = [
  {
    name: 'Create Articles',
    admin: true,
    editor: true,
    writer: true,
    subscriber: false
  },
  {
    name: 'Edit Any Article',
    admin: true,
    editor: true,
    writer: false,
    subscriber: false
  },
  {
    name: 'Delete Articles',
    admin: true,
    editor: false,
    writer: false,
    subscriber: false
  },
  {
    name: 'Manage Users',
    admin: true,
    editor: false,
    writer: false,
    subscriber: false
  },
  {
    name: 'Change Site Settings',
    admin: true,
    editor: false,
    writer: false,
    subscriber: false
  }
]

export default function UserManagementPage() {
  const [users, setUsers] = useState<User[]>(mockUsers)
  const [searchTerm, setSearchTerm] = useState('')
  const [roleFilter, setRoleFilter] = useState('All Roles')
  const [permissionSettings, setPermissionSettings] = useState(permissions)

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesRole = roleFilter === 'All Roles' || user.role === roleFilter
    return matchesSearch && matchesRole
  })

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'Admin': return 'bg-red-100 text-red-800'
      case 'Editor': return 'bg-blue-100 text-blue-800'
      case 'Writer': return 'bg-green-100 text-green-800'
      case 'Subscriber': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusColor = (status: string) => {
    return status === 'Active'
      ? 'bg-green-100 text-green-800'
      : 'bg-red-100 text-red-800'
  }

  return (
    <ProtectedRoute requiredRoles={['admin']}>
      <AdminLayout>
        <div className="space-y-8">
          {/* Header */}
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">User Management</h1>
              <p className="text-gray-600 mt-1">Manage users and their permissions</p>
            </div>
            <Button className="bg-red-600 hover:bg-red-700">
              <UserPlus className="h-4 w-4 mr-2" />
              Add User
            </Button>
          </div>

          {/* Search and Filter */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search users..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={roleFilter} onValueChange={setRoleFilter}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="All Roles">All Roles</SelectItem>
                <SelectItem value="Admin">Admin</SelectItem>
                <SelectItem value="Editor">Editor</SelectItem>
                <SelectItem value="Writer">Writer</SelectItem>
                <SelectItem value="Subscriber">Subscriber</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Users Table */}
          <Card>
            <CardHeader>
              <CardTitle>Users</CardTitle>
              <CardDescription>
                Manage user accounts and their access levels
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Name</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Email</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Role</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Last Login</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredUsers.map((user) => (
                      <tr key={user.id} className="border-b hover:bg-gray-50">
                        <td className="py-3 px-4">{user.name}</td>
                        <td className="py-3 px-4 text-gray-600">{user.email}</td>
                        <td className="py-3 px-4">
                          <Badge className={getRoleColor(user.role)}>
                            {user.role}
                          </Badge>
                        </td>
                        <td className="py-3 px-4">
                          <Badge className={getStatusColor(user.status)}>
                            {user.status}
                          </Badge>
                        </td>
                        <td className="py-3 px-4 text-gray-600">{user.lastLogin}</td>
                        <td className="py-3 px-4">
                          <div className="flex items-center space-x-2">
                            <Button variant="ghost" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-800">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>

          {/* Permission Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Permission Settings</CardTitle>
              <CardDescription>
                Define what users can do based on their roles
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Permission</th>
                      <th className="text-center py-3 px-4 font-medium text-gray-900">Admin</th>
                      <th className="text-center py-3 px-4 font-medium text-gray-900">Editor</th>
                      <th className="text-center py-3 px-4 font-medium text-gray-900">Writer</th>
                      <th className="text-center py-3 px-4 font-medium text-gray-900">Subscriber</th>
                    </tr>
                  </thead>
                  <tbody>
                    {permissionSettings.map((permission, index) => (
                      <tr key={permission.name} className="border-b hover:bg-gray-50">
                        <td className="py-3 px-4 font-medium">{permission.name}</td>
                        <td className="py-3 px-4 text-center">
                          <Switch
                            checked={permission.admin}
                            onCheckedChange={(checked) => {
                              const updated = [...permissionSettings]
                              updated[index].admin = checked
                              setPermissionSettings(updated)
                            }}
                          />
                        </td>
                        <td className="py-3 px-4 text-center">
                          <Switch
                            checked={permission.editor}
                            onCheckedChange={(checked) => {
                              const updated = [...permissionSettings]
                              updated[index].editor = checked
                              setPermissionSettings(updated)
                            }}
                          />
                        </td>
                        <td className="py-3 px-4 text-center">
                          <Switch
                            checked={permission.writer}
                            onCheckedChange={(checked) => {
                              const updated = [...permissionSettings]
                              updated[index].writer = checked
                              setPermissionSettings(updated)
                            }}
                          />
                        </td>
                        <td className="py-3 px-4 text-center">
                          <Switch
                            checked={permission.subscriber}
                            onCheckedChange={(checked) => {
                              const updated = [...permissionSettings]
                              updated[index].subscriber = checked
                              setPermissionSettings(updated)
                            }}
                          />
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </div>
      </AdminLayout>
    </ProtectedRoute>
  )
}
