import { createSupabaseServerClient } from './supabase-server'
import { Article, Category, User } from './types'

// Server-side database operations (for use in server components and API routes)
export class DatabaseServer {
  private async getSupabase() {
    return await createSupabaseServerClient()
  }

  async getPublishedArticles(options: {
    limit?: number
    offset?: number
    categorySlug?: string
    isFeatured?: boolean
    isBreaking?: boolean
  } = {}) {
    const { limit = 10, offset = 0, categorySlug, isFeatured, isBreaking } = options
    const supabase = await this.getSupabase()

    const { data, error } = await supabase.rpc('get_published_articles', {
      limit_count: limit,
      offset_count: offset,
      category_slug: categorySlug || null,
      is_featured_only: isFeatured || false,
      is_breaking_only: isBreaking || false
    })

    if (error) throw error
    return data
  }

  async getArticleBySlug(slug: string) {
    const supabase = await this.getSupabase()
    const { data, error } = await supabase.rpc('get_article_by_slug', {
      article_slug: slug
    })

    if (error) throw error
    return data?.[0] || null
  }

  async getCategories() {
    const supabase = await this.getSupabase()
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .order('name')

    if (error) throw error
    return data
  }

  async getCategoryBySlug(slug: string) {
    const supabase = await this.getSupabase()
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('slug', slug)
      .single()

    if (error) throw error
    return data
  }

  async getUserProfile(userId: string) {
    const supabase = await this.getSupabase()
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single()

    if (error) throw error
    return data
  }

  async getArticlesByCategory(categorySlug: string, options: {
    limit?: number
    offset?: number
  } = {}) {
    const { limit = 10, offset = 0 } = options
    const supabase = await this.getSupabase()

    const { data, error } = await supabase.rpc('get_published_articles', {
      limit_count: limit,
      offset_count: offset,
      category_slug: categorySlug,
      is_featured_only: false,
      is_breaking_only: false
    })

    if (error) throw error
    return data
  }

  async searchArticles(query: string, options: {
    limit?: number
    offset?: number
  } = {}) {
    const { limit = 10, offset = 0 } = options
    const supabase = await this.getSupabase()

    const { data, error } = await supabase.rpc('search_articles', {
      search_query: query,
      limit_count: limit,
      offset_count: offset
    })

    if (error) throw error
    return data
  }

  async getRecentArticles(limit: number = 5) {
    const supabase = await this.getSupabase()
    const { data, error } = await supabase
      .from('articles')
      .select(`
        id,
        title,
        slug,
        excerpt,
        featured_image,
        published_at,
        categories!inner(name, slug),
        profiles!inner(full_name)
      `)
      .eq('is_published', true)
      .order('published_at', { ascending: false })
      .limit(limit)

    if (error) throw error
    return data
  }

  async getFeaturedArticles(limit: number = 3) {
    const supabase = await this.getSupabase()
    const { data, error } = await supabase
      .from('articles')
      .select(`
        id,
        title,
        slug,
        excerpt,
        featured_image,
        published_at,
        categories!inner(name, slug),
        profiles!inner(full_name)
      `)
      .eq('is_published', true)
      .eq('is_featured', true)
      .order('published_at', { ascending: false })
      .limit(limit)

    if (error) throw error
    return data
  }

  async getBreakingNews(limit: number = 5) {
    const supabase = await this.getSupabase()
    const { data, error } = await supabase
      .from('articles')
      .select(`
        id,
        title,
        slug,
        excerpt,
        featured_image,
        published_at,
        categories!inner(name, slug),
        profiles!inner(full_name)
      `)
      .eq('is_published', true)
      .eq('is_breaking', true)
      .order('published_at', { ascending: false })
      .limit(limit)

    if (error) throw error
    return data
  }
}

// Export instance
export const dbServer = new DatabaseServer()
