import { Suspense } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { TraditionalHomepage } from '@/components/home/<USER>'

export default function Home() {
  return (
    <MainLayout>
      <Suspense fallback={
        <div className="min-h-screen bg-white">
          <div className="animate-pulse">
            <div className="h-64 bg-gray-200 mb-4"></div>
            <div className="grid grid-cols-12 gap-4 max-w-7xl mx-auto px-4">
              <div className="col-span-8 space-y-4">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="h-32 bg-gray-200"></div>
                ))}
              </div>
              <div className="col-span-4 space-y-4">
                {[...Array(8)].map((_, i) => (
                  <div key={i} className="h-24 bg-gray-200"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      }>
        <TraditionalHomepage />
      </Suspense>
    </MainLayout>
  )
}
