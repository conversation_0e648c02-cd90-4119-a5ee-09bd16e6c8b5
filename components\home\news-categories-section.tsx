'use client'

import { useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { formatDistanceToNow } from 'date-fns'

interface Article {
  id: string
  title: string
  excerpt: string
  image_url?: string
  published_at: string
  category: string
  slug: string
}

interface NewsCategoriesSectionProps {
  articles: Article[]
}

const categories = [
  { name: 'Politics', slug: 'politics', color: 'red' },
  { name: 'Tech', slug: 'technology', color: 'blue' },
  { name: 'World', slug: 'world', color: 'green' },
  { name: 'Sports', slug: 'sports', color: 'orange' },
  { name: 'Entertainment', slug: 'entertainment', color: 'purple' },
]

export function NewsCategoriesSection({ articles }: NewsCategoriesSectionProps) {
  const [activeTab, setActiveTab] = useState('Politics')

  // Filter articles by category
  const getArticlesByCategory = (categorySlug: string) => {
    return articles.filter(article => 
      article.category.toLowerCase() === categorySlug.toLowerCase()
    ).slice(0, 6)
  }

  const activeCategory = categories.find(cat => cat.name === activeTab)
  const categoryArticles = getArticlesByCategory(activeCategory?.slug || 'politics')

  return (
    <div className="bg-white py-8">
      <div className="max-w-7xl mx-auto px-4">
        <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
          News Categories
        </h2>
        
        {/* Category Tabs */}
        <div className="flex justify-center mb-8">
          <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
            {categories.map((category) => (
              <button
                key={category.name}
                onClick={() => setActiveTab(category.name)}
                className={`px-6 py-2 rounded-md font-medium transition-colors ${
                  activeTab === category.name
                    ? 'bg-red-600 text-white'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                {category.name}
              </button>
            ))}
          </div>
        </div>

        {/* Articles Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {categoryArticles.map((article) => (
            <Link key={article.id} href={`/article/${article.slug}`}>
              <div className="group cursor-pointer bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
                <div className="relative h-48 overflow-hidden">
                  {article.image_url ? (
                    <Image
                      src={article.image_url}
                      alt={article.title}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  ) : (
                    <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                      <span className="text-gray-500">No Image</span>
                    </div>
                  )}
                  <div className="absolute top-4 left-4">
                    <span className="bg-red-600 text-white px-3 py-1 text-xs font-bold rounded">
                      {article.category}
                    </span>
                  </div>
                </div>
                <div className="p-4">
                  <h3 className="text-lg font-semibold text-gray-900 line-clamp-2 group-hover:text-red-600 transition-colors mb-2">
                    {article.title}
                  </h3>
                  <p className="text-gray-600 text-sm line-clamp-2 mb-3">
                    {article.excerpt}
                  </p>
                  <p className="text-gray-500 text-xs">
                    {formatDistanceToNow(new Date(article.published_at), {
                      addSuffix: true
                    })}
                  </p>
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* View All Button */}
        <div className="text-center mt-8">
          <Link 
            href={`/category/${activeCategory?.slug}`}
            className="inline-block bg-red-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-red-700 transition-colors"
          >
            View All {activeTab} News
          </Link>
        </div>
      </div>
    </div>
  )
}
