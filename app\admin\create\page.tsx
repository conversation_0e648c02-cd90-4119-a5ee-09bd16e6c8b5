'use client'

import { ProtectedRoute } from '@/components/auth/protected-route'
import { ArticleForm } from '@/components/articles/article-form'

export default function CreateArticlePage() {
  return (
    <ProtectedRoute requiredRoles={['admin', 'editor', 'writer']}>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Create New Article</h1>
            <p className="text-gray-600 mt-2">
              Write and publish your article to share with the world.
            </p>
          </div>
          
          <ArticleForm />
        </div>
      </div>
    </ProtectedRoute>
  )
}
