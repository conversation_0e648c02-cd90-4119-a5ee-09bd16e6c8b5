'use client'

import { AdminLayout } from '@/components/admin/admin-layout'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { ArticleForm } from '@/components/articles/article-form'

export default function CreateArticlePage() {
  return (
    <ProtectedRoute requiredRoles={['admin', 'editor', 'writer']}>
      <AdminLayout>
        <div className="space-y-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Create New Article</h1>
            <p className="text-gray-600 mt-1">
              Write and publish your article to share with the world.
            </p>
          </div>

          <ArticleForm />
        </div>
      </AdminLayout>
    </ProtectedRoute>
  )
}
