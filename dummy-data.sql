-- Dummy Data for News Website
-- Run this AFTER running database-setup.sql

-- Insert Categories
INSERT INTO categories (name, slug, description) VALUES
('राजनीति', 'politics', 'राजनीतिक समाचार और अपडेट'),
('खेल', 'sports', 'खेल जगत की ताज़ा खबरें'),
('मनोरंजन', 'entertainment', 'बॉलीवुड और मनोरंजन की दुनिया'),
('तकनीक', 'technology', 'तकनीकी समाचार और नवाचार'),
('व्यापार', 'business', 'व्यापार और अर्थव्यवस्था'),
('स्वास्थ्य', 'health', 'स्वास्थ्य और कल्याण'),
('शिक्षा', 'education', 'शिक्षा जगत की खबरें'),
('अंतर्राष्ट्रीय', 'international', 'विदेशी समाचार');

-- Insert Sample Articles
INSERT INTO articles (title, slug, excerpt, content, category, status, is_featured, is_breaking, published_at) VALUES
('भारत में नई तकनीकी क्रांति का आगाज़', 'new-tech-revolution-india', 'भारत में आर्टिफिशियल इंटेलिजेंस और मशीन लर्निंग के क्षेत्र में नई उपलब्धियां हासिल हो रही हैं।', 'भारत में तकनीकी क्षेत्र में एक नई क्रांति का आगाज़ हो रहा है। आर्टिफिशियल इंटेलिजेंस, मशीन लर्निंग, और ब्लॉकचेन जैसी नई तकनीकों के कारण भारतीय स्टार्टअप्स दुनिया भर में अपनी पहचान बना रहे हैं। इस क्रांति का असर न केवल तकनीकी कंपनियों पर हो रहा है बल्कि पारंपरिक व्यापार भी इससे प्रभावित हो रहे हैं।', 'तकनीक', 'published', true, true, NOW() - INTERVAL '1 hour'),

('क्रिकेट विश्व कप में भारत की शानदार जीत', 'india-cricket-world-cup-victory', 'भारतीय क्रिकेट टीम ने विश्व कप के फाइनल में शानदार प्रदर्शन करके जीत हासिल की।', 'भारतीय क्रिकेट टीम ने आज विश्व कप के फाइनल मैच में अपने प्रतिद्वंद्वी को हराकर चैंपियनशिप जीती है। कप्तान की बेहतरीन कप्तानी और टीम के सभी खिलाड़ियों के शानदार प्रदर्शन से यह जीत संभव हुई है। पूरे देश में जश्न का माहौल है।', 'खेल', 'published', true, false, NOW() - INTERVAL '2 hours'),

('बॉलीवुड में नई फिल्म का धमाकेदार ट्रेलर रिलीज़', 'bollywood-new-movie-trailer-release', 'बॉलीवुड की सबसे प्रतीक्षित फिल्म का ट्रेलर आज रिलीज़ हुआ और दर्शकों में उत्साह की लहर दौड़ गई।', 'बॉलीवुड की सबसे बड़ी फिल्म का ट्रेलर आज रिलीज़ हुआ है। इस फिल्म में इंडस्ट्री के सबसे बड़े सितारे नज़र आ रहे हैं। ट्रेलर देखने के बाद दर्शकों में फिल्म को लेकर काफी उत्साह है। फिल्म अगले महीने सिनेमाघरों में रिलीज़ होगी।', 'मनोरंजन', 'published', false, false, NOW() - INTERVAL '3 hours'),

('शेयर बाज़ार में तेज़ी, सेंसेक्स नए रिकॉर्ड पर', 'stock-market-sensex-new-record', 'आज शेयर बाज़ार में तेज़ी देखी गई और सेंसेक्स ने नया रिकॉर्ड बनाया।', 'आज भारतीय शेयर बाज़ार में जबरदस्त तेज़ी देखी गई। सेंसेक्स ने 75,000 का आंकड़ा पार करके नया रिकॉर्ड बनाया है। निफ्टी भी अपने सर्वकालिक उच्च स्तर पर पहुंच गया है। विशेषज्ञों का कहना है कि यह तेज़ी आर्थिक सुधारों और विदेशी निवेश में वृद्धि के कारण है।', 'व्यापार', 'published', false, false, NOW() - INTERVAL '4 hours'),

('नई शिक्षा नीति से मिलेंगे बेहतर अवसर', 'new-education-policy-opportunities', 'नई राष्ट्रीय शिक्षा नीति के तहत छात्रों को मिलेंगे बेहतर अवसर और गुणवत्तापूर्ण शिक्षा।', 'नई राष्ट्रीय शिक्षा नीति के कार्यान्वयन से देश की शिक्षा व्यवस्था में क्रांतिकारी बदलाव आने की उम्मीद है। इस नीति के तहत छात्रों को अधिक लचीलापन मिलेगा और वे अपनी रुचि के अनुसार विषयों का चयन कर सकेंगे। डिजिटल शिक्षा पर भी विशेष जोर दिया गया है।', 'शिक्षा', 'published', false, false, NOW() - INTERVAL '5 hours'),

('स्वास्थ्य मंत्रालय की नई योजना से मिलेगी बेहतर सुविधा', 'health-ministry-new-scheme', 'स्वास्थ्य मंत्रालय ने ग्रामीण क्षेत्रों में बेहतर चिकित्सा सुविधा उपलब्ध कराने के लिए नई योजना शुरू की है।', 'केंद्रीय स्वास्थ्य मंत्रालय ने ग्रामीण क्षेत्रों में स्वास्थ्य सेवाओं को बेहतर बनाने के लिए एक नई योजना की शुरुआत की है। इस योजना के तहत दूरदराज के इलाकों में भी गुणवत्तापूर्ण चिकित्सा सुविधा उपलब्ध कराई जाएगी। टेलीमेडिसिन सेवाओं का भी विस्तार किया जाएगा।', 'स्वास्थ्य', 'published', false, false, NOW() - INTERVAL '6 hours'),

('अंतर्राष्ट्रीय व्यापार में भारत की बढ़ती भागीदारी', 'india-international-trade-growth', 'भारत का अंतर्राष्ट्रीय व्यापार में योगदान लगातार बढ़ रहा है और नए रिकॉर्ड बन रहे हैं।', 'भारत का अंतर्राष्ट्रीय व्यापार में योगदान लगातार बढ़ रहा है। निर्यात में वृद्धि और नए बाज़ारों में प्रवेश के कारण भारत की आर्थिक स्थिति मजबूत हो रही है। विशेष रूप से तकनीकी सेवाओं और फार्मास्यूटिकल्स के क्षेत्र में भारत की पहुंच बढ़ी है।', 'अंतर्राष्ट्रीय', 'published', false, false, NOW() - INTERVAL '7 hours'),

('राजनीतिक दलों में नई रणनीति पर चर्चा', 'political-parties-new-strategy', 'आगामी चुनावों को देखते हुए राजनीतिक दलों में नई रणनीति पर चर्चा हो रही है।', 'आगामी चुनावों को देखते हुए सभी प्रमुख राजनीतिक दल अपनी रणनीति तैयार कर रहे हैं। जनता के मुद्दों को लेकर दलों में व्यापक चर्चा हो रही है। विकास, रोजगार, और सामाजिक न्याय जैसे मुद्दे चुनावी रणनीति के केंद्र में हैं।', 'राजनीति', 'published', false, true, NOW() - INTERVAL '8 hours');

-- Insert Sample Advertisements
INSERT INTO advertisements (title, description, image_url, link_url, position, is_active, start_date, end_date) VALUES
('स्मार्टफोन की नई रेंज', 'सबसे कम कीमत में सबसे अच्छा स्मार्टफोन। अभी खरीदें और पाएं 50% तक की छूट।', 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400', 'https://example.com/smartphone', 'header', true, NOW(), NOW() + INTERVAL '30 days'),

('ऑनलाइन कोर्स - 70% छूट', 'प्रोग्रामिंग, डिज़ाइन, और बिज़नेस के कोर्स सीखें। सीमित समय के लिए 70% छूट।', 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400', 'https://example.com/courses', 'sidebar', true, NOW(), NOW() + INTERVAL '15 days'),

('होम लोन - कम ब्याज दर', 'सबसे कम ब्याज दर पर होम लोन पाएं। तुरंत अप्रूवल और आसान EMI।', 'https://images.unsplash.com/photo-1560518883-ce09059eeffa?w=400', 'https://example.com/homeloan', 'content', true, NOW(), NOW() + INTERVAL '45 days'),

('हेल्थ इंश्योरेंस', 'परिवार के लिए सबसे बेहतर हेल्थ इंश्योरेंस। कैशलेस ट्रीटमेंट की सुविधा।', 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=400', 'https://example.com/insurance', 'sidebar', true, NOW(), NOW() + INTERVAL '60 days'),

('ऑनलाइन शॉपिंग - मेगा सेल', 'सभी कैटेगरी में 80% तक की छूट। फ्री होम डिलीवरी और आसान रिटर्न।', 'https://images.unsplash.com/photo-1472851294608-062f824d29cc?w=400', 'https://example.com/shopping', 'footer', true, NOW(), NOW() + INTERVAL '20 days'),

('कार लोन - तुरंत अप्रूवल', 'सपनों की कार खरीदें। कम ब्याज दर और तुरंत अप्रूवल की गारंटी।', 'https://images.unsplash.com/photo-1549924231-f129b911e442?w=400', 'https://example.com/carloan', 'content', true, NOW(), NOW() + INTERVAL '35 days');

-- Note: You'll need to create a user first through Supabase Auth to get user IDs for the author_id field
-- For now, articles are created without author_id (will be NULL)

-- Update some articles to have image URLs
UPDATE articles SET image_url = 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=600' WHERE slug = 'new-tech-revolution-india';
UPDATE articles SET image_url = 'https://images.unsplash.com/photo-1540747913346-19e32dc3e97e?w=600' WHERE slug = 'india-cricket-world-cup-victory';
UPDATE articles SET image_url = 'https://images.unsplash.com/photo-1489599904472-84978f312f2e?w=600' WHERE slug = 'bollywood-new-movie-trailer-release';
UPDATE articles SET image_url = 'https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?w=600' WHERE slug = 'stock-market-sensex-new-record';
UPDATE articles SET image_url = 'https://images.unsplash.com/photo-1503676260728-1c00da094a0b?w=600' WHERE slug = 'new-education-policy-opportunities';
UPDATE articles SET image_url = 'https://images.unsplash.com/photo-**********-0eb30cd8c063?w=600' WHERE slug = 'health-ministry-new-scheme';
UPDATE articles SET image_url = 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=600' WHERE slug = 'india-international-trade-growth';
UPDATE articles SET image_url = 'https://images.unsplash.com/photo-1529107386315-e1a2ed48a620?w=600' WHERE slug = 'political-parties-new-strategy';
