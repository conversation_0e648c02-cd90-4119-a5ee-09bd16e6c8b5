# NewsPortal - Modern News Website

A comprehensive news website built with Next.js, TypeScript, Tailwind CSS, and Supabase. Features include role-based authentication, article management, rich text editing, and a responsive design.

## 🚀 Features

### 🔐 Authentication & Authorization
- **Supabase Auth** - Email/password authentication
- **Role-based Access Control** - Admin, Editor, Writer, Guest roles
- **Protected Routes** - Secure admin areas
- **Session Management** - Persistent login state

### 📝 Content Management
- **Rich Text Editor** - TipTap-based WYSIWYG editor
- **Article CRUD** - Create, read, update, delete articles
- **Image Upload** - Supabase Storage integration
- **Category System** - Organize articles by categories
- **Featured Articles** - Highlight important content
- **Breaking News** - Special breaking news ticker

### 🎨 User Interface
- **Responsive Design** - Mobile-first approach
- **Modern UI** - shadcn/ui components
- **Dark/Light Mode Ready** - Built-in theme support
- **Loading States** - Smooth user experience
- **Error Handling** - Graceful error boundaries

### 📊 Admin Dashboard
- **Analytics Overview** - Content and user statistics
- **User Management** - Role assignment and user control
- **Category Management** - Create and manage categories
- **Article Approval** - Editorial workflow

### 🔍 Additional Features
- **Search Functionality** - Find articles by keywords
- **Newsletter Signup** - Email subscription system
- **SEO Optimized** - Meta tags and Open Graph
- **Performance Optimized** - Image optimization and caching

## 🛠️ Tech Stack

- **Framework**: Next.js 15 (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS 4
- **UI Components**: shadcn/ui
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **Storage**: Supabase Storage
- **Rich Text**: TipTap
- **Forms**: React Hook Form + Zod
- **Icons**: Lucide React
- **Deployment**: Vercel (recommended)

## 📋 Prerequisites

- Node.js 18+
- npm/yarn/pnpm
- Supabase account

## 🚀 Quick Start

### 1. Clone the Repository

```bash
git clone <your-repo-url>
cd news-web
```

### 2. Install Dependencies

```bash
npm install
# or
yarn install
# or
pnpm install
```

### 3. Set Up Supabase

1. Create a new project at [supabase.com](https://supabase.com)
2. Copy your project credentials
3. Follow the detailed setup guide in `SUPABASE_SETUP.md`

### 4. Environment Variables

Create a `.env.local` file in the root directory:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Next.js Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret

# App Configuration
NEXT_PUBLIC_APP_NAME="News Portal"
NEXT_PUBLIC_APP_DESCRIPTION="Modern News Website"
```

### 5. Database Setup

Run the SQL migrations in your Supabase SQL editor:

1. `supabase/migrations/001_initial_schema.sql`
2. `supabase/migrations/002_rls_policies.sql`
3. `supabase/migrations/003_functions.sql`

### 6. Start Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to see your application.

## 📁 Project Structure

```
news-web/
├── app/                    # Next.js App Router
│   ├── admin/             # Admin dashboard pages
│   ├── article/           # Article detail pages
│   ├── auth/              # Authentication pages
│   ├── category/          # Category pages
│   ├── search/            # Search functionality
│   └── globals.css        # Global styles
├── components/            # React components
│   ├── admin/             # Admin-specific components
│   ├── articles/          # Article-related components
│   ├── auth/              # Authentication components
│   ├── editor/            # Rich text editor
│   ├── home/              # Homepage components
│   ├── layout/            # Layout components
│   └── ui/                # shadcn/ui components
├── lib/                   # Utility libraries
│   ├── auth-context.tsx   # Authentication context
│   ├── database.ts        # Database operations
│   ├── supabase.ts        # Supabase client
│   ├── types.ts           # TypeScript types
│   └── utils.ts           # Utility functions
├── supabase/              # Database migrations
│   └── migrations/        # SQL migration files
└── public/                # Static assets
```

## 🔧 Configuration

### User Roles

- **Admin**: Full system access, user management
- **Editor**: Article management, publishing control
- **Writer**: Create and edit own articles
- **Guest**: Read-only access

### Default Categories

The system comes with pre-configured categories:
- Politics
- Business
- Sports
- Technology
- Entertainment
- Health
- World
- Local

## 🚀 Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy automatically

### Other Platforms

The application can be deployed on any platform that supports Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
1. Check the `SUPABASE_SETUP.md` guide
2. Review the troubleshooting section
3. Open an issue on GitHub

## 🔮 Future Enhancements

- [ ] Comment system
- [ ] Social media sharing
- [ ] Advanced search with filters
- [ ] Email notifications
- [ ] Multi-language support
- [ ] PWA capabilities
- [ ] Advanced analytics
- [ ] Content scheduling
