'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { db } from '@/lib/database'
import { toast } from 'react-hot-toast'
import { Mail, Loader2 } from 'lucide-react'

export function NewsletterSignup() {
  const [email, setEmail] = useState('')
  const [loading, setLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!email) {
      toast.error('Please enter your email address')
      return
    }

    setLoading(true)

    try {
      await db.subscribeToNewsletter(email)
      toast.success('Successfully subscribed to our newsletter!')
      setEmail('')
    } catch (error: any) {
      if (error.message?.includes('duplicate')) {
        toast.error('You are already subscribed to our newsletter')
      } else {
        toast.error('Failed to subscribe. Please try again.')
      }
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="text-center max-w-2xl mx-auto">
      <div className="mb-6">
        <Mail className="h-12 w-12 text-blue-400 mx-auto mb-4" />
        <h2 className="text-3xl font-bold mb-4">Stay Updated</h2>
        <p className="text-gray-300 text-lg">
          Get the latest news and updates delivered straight to your inbox. 
          Join thousands of readers who trust us for reliable news.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
        <Input
          type="email"
          placeholder="Enter your email address"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          className="flex-1 bg-gray-800 border-gray-700 text-white placeholder-gray-400"
          disabled={loading}
          required
        />
        <Button 
          type="submit" 
          className="bg-blue-600 hover:bg-blue-700 px-8"
          disabled={loading}
        >
          {loading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Subscribing...
            </>
          ) : (
            'Subscribe'
          )}
        </Button>
      </form>

      <p className="text-gray-400 text-sm mt-4">
        We respect your privacy. Unsubscribe at any time.
      </p>
    </div>
  )
}
