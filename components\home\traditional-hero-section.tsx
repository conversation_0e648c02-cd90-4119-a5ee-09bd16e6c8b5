'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { formatDistanceToNow } from 'date-fns'
import { hi } from 'date-fns/locale'

interface Article {
  id: string
  title: string
  excerpt: string
  image_url?: string
  published_at: string
  category: string
  slug: string
}

interface TraditionalHeroSectionProps {
  mainStory?: Article
  featuredStories: Article[]
  breakingNews: Article[]
}

export function TraditionalHeroSection({ 
  mainStory, 
  featuredStories, 
  breakingNews 
}: TraditionalHeroSectionProps) {
  const [currentBreaking, setCurrentBreaking] = useState(0)

  // Auto-rotate breaking news
  useEffect(() => {
    if (breakingNews.length > 1) {
      const interval = setInterval(() => {
        setCurrentBreaking((prev) => (prev + 1) % breakingNews.length)
      }, 4000)
      return () => clearInterval(interval)
    }
  }, [breakingNews.length])

  return (
    <div className="bg-white">
      {/* Main Hero Content */}
      <div className="max-w-7xl mx-auto px-4 py-6">
        <div className="grid grid-cols-12 gap-6">
          {/* Main Story */}
          {mainStory && (
            <div className="col-span-12 lg:col-span-8">
              <div className="relative group cursor-pointer">
                <Link href={`/article/${mainStory.slug}`}>
                  <div className="relative h-96 overflow-hidden rounded-lg">
                    {mainStory.image_url ? (
                      <Image
                        src={mainStory.image_url}
                        alt={mainStory.title}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                    ) : (
                      <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                        <span className="text-gray-500">No Image</span>
                      </div>
                    )}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent" />
                    <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
                      <div className="inline-block bg-red-600 text-white px-3 py-1 text-sm font-bold mb-3 rounded">
                        {mainStory.category}
                      </div>
                      <h1 className="text-2xl lg:text-3xl font-bold mb-3 leading-tight">
                        {mainStory.title}
                      </h1>
                      <p className="text-gray-200 text-lg mb-2 line-clamp-2">
                        {mainStory.excerpt}
                      </p>
                      <p className="text-gray-300 text-sm">
                        {formatDistanceToNow(new Date(mainStory.published_at), {
                          addSuffix: true
                        })}
                      </p>
                    </div>
                  </div>
                </Link>
              </div>
            </div>
          )}

          {/* Featured Stories Sidebar */}
          <div className="col-span-12 lg:col-span-4">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h2 className="text-xl font-bold text-gray-900 mb-4 border-b-2 border-red-600 pb-2">
                Editor's Picks
              </h2>
              <div className="space-y-4">
                {featuredStories.map((story, index) => (
                  <Link key={story.id} href={`/article/${story.slug}`}>
                    <div className="flex gap-3 group cursor-pointer">
                      <div className="flex-shrink-0 w-20 h-16 relative overflow-hidden rounded">
                        {story.image_url ? (
                          <Image
                            src={story.image_url}
                            alt={story.title}
                            fill
                            className="object-cover group-hover:scale-105 transition-transform duration-300"
                          />
                        ) : (
                          <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                            <span className="text-xs text-gray-500">IMG</span>
                          </div>
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="text-sm font-semibold text-gray-900 line-clamp-2 group-hover:text-red-600 transition-colors">
                          {story.title}
                        </h3>
                        <p className="text-xs text-gray-500 mt-1">
                          {formatDistanceToNow(new Date(story.published_at), {
                            addSuffix: true
                          })}
                        </p>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
