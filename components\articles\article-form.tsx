'use client'

import { useState, useEffect } from 'react'
import { use<PERSON>out<PERSON> } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { RichTextEditor } from '@/components/editor/rich-text-editor'
import { db } from '@/lib/database'
import { useAuth } from '@/lib/auth-context'
import { usePermissions } from '@/components/auth/protected-route'
import { toast } from 'react-hot-toast'
import { Loader2, Upload, Save, Eye, ChevronDown } from 'lucide-react'
import { Article, Category } from '@/lib/types'

const articleSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title too long'),
  excerpt: z.string().max(500, 'Excerpt too long').optional(),
  content: z.string().min(1, 'Content is required'),
  category_id: z.string().min(1, 'Category is required'),
  featured_image: z.string().url('Invalid image URL').optional().or(z.literal('')),
  is_featured: z.boolean().default(false),
  is_breaking: z.boolean().default(false),
  is_published: z.boolean().default(false),
})

type ArticleFormData = z.infer<typeof articleSchema>

interface ArticleFormProps {
  article?: Article
  onSuccess?: () => void
}

export function ArticleForm({ article, onSuccess }: ArticleFormProps) {
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(false)
  const [imageUploading, setImageUploading] = useState(false)
  const { user } = useAuth()
  const { canPublishArticles } = usePermissions()
  const router = useRouter()

  const form = useForm<ArticleFormData>({
    resolver: zodResolver(articleSchema),
    defaultValues: {
      title: article?.title || '',
      excerpt: article?.excerpt || '',
      content: article?.content || '',
      category_id: article?.category_id || '',
      featured_image: article?.featured_image || '',
      is_featured: article?.is_featured || false,
      is_breaking: article?.is_breaking || false,
      is_published: article?.is_published || false,
    },
  })

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const categoriesData = await db.getCategories()
        setCategories(categoriesData)
      } catch (error) {
        toast.error('Failed to load categories')
      }
    }

    fetchCategories()
  }, [])

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    if (file.size > 5 * 1024 * 1024) {
      toast.error('Image size must be less than 5MB')
      return
    }

    setImageUploading(true)
    try {
      const imageUrl = await db.uploadImage(file, 'articles')
      form.setValue('featured_image', imageUrl)
      toast.success('Image uploaded successfully')
    } catch (error) {
      toast.error('Failed to upload image')
    } finally {
      setImageUploading(false)
    }
  }

  const onSubmit = async (data: ArticleFormData) => {
    if (!user) {
      toast.error('You must be logged in to create articles')
      return
    }

    setLoading(true)
    try {
      if (article) {
        // Update existing article
        await db.updateArticle({
          id: article.id,
          ...data,
        })
        toast.success('Article updated successfully')
      } else {
        // Create new article
        await db.createArticle({
          ...data,
          author_id: user.id,
        })
        toast.success('Article created successfully')
      }

      onSuccess?.()
      router.push('/admin')
    } catch (error: any) {
      toast.error(error.message || 'Failed to save article')
    } finally {
      setLoading(false)
    }
  }

  const selectedCategory = categories.find(cat => cat.id === form.watch('category_id'))

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>
            {article ? 'Edit Article' : 'Create New Article'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Title */}
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Title</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter article title" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Excerpt */}
              <FormField
                control={form.control}
                name="excerpt"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Excerpt (Optional)</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Brief summary of the article"
                        rows={3}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Category */}
              <FormField
                control={form.control}
                name="category_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Category</FormLabel>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" className="w-full justify-between">
                          {selectedCategory?.name || 'Select category'}
                          <ChevronDown className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent className="w-full">
                        {categories.map((category) => (
                          <DropdownMenuItem
                            key={category.id}
                            onClick={() => field.onChange(category.id)}
                          >
                            {category.name}
                          </DropdownMenuItem>
                        ))}
                      </DropdownMenuContent>
                    </DropdownMenu>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Featured Image */}
              <FormField
                control={form.control}
                name="featured_image"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Featured Image</FormLabel>
                    <div className="space-y-2">
                      <div className="flex gap-2">
                        <Input
                          placeholder="Image URL or upload below"
                          {...field}
                        />
                        <div className="relative">
                          <input
                            type="file"
                            accept="image/*"
                            onChange={handleImageUpload}
                            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                            disabled={imageUploading}
                          />
                          <Button type="button" variant="outline" disabled={imageUploading}>
                            {imageUploading ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <Upload className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </div>
                      {field.value && (
                        <img
                          src={field.value}
                          alt="Preview"
                          className="w-32 h-24 object-cover rounded border"
                        />
                      )}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Content */}
              <FormField
                control={form.control}
                name="content"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Content</FormLabel>
                    <FormControl>
                      <RichTextEditor
                        content={field.value}
                        onChange={field.onChange}
                        placeholder="Write your article content here..."
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Options */}
              {canPublishArticles() && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      {...form.register('is_featured')}
                      className="rounded"
                    />
                    <span className="text-sm">Featured Article</span>
                  </label>
                  
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      {...form.register('is_breaking')}
                      className="rounded"
                    />
                    <span className="text-sm">Breaking News</span>
                  </label>
                  
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      {...form.register('is_published')}
                      className="rounded"
                    />
                    <span className="text-sm">Publish Now</span>
                  </label>
                </div>
              )}

              {/* Actions */}
              <div className="flex gap-4">
                <Button type="submit" disabled={loading}>
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      {article ? 'Update Article' : 'Create Article'}
                    </>
                  )}
                </Button>
                
                <Button type="button" variant="outline" onClick={() => router.back()}>
                  Cancel
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  )
}
