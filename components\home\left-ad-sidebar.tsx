'use client'

import { AdBanner } from '@/components/advertisements/ad-banner'

export function LeftAdSidebar() {
  return (
    <div className="sticky top-4 space-y-3 bg-white p-2 rounded-lg border border-gray-200 shadow-sm">
      {/* Sidebar Header */}
      <div className="text-center py-2 border-b border-gray-200 mb-3">
        <h3 className="text-sm font-bold text-gray-800">विज्ञापन</h3>
      </div>

      {/* Main Advertisement Banner */}
      <AdBanner position="sidebar" maxAds={1} className="mb-3" />
      
      {/* Advertisement Cards Stack */}
      <div className="space-y-3">
        {/* Featured Advertisement Card */}
        <div className="bg-gradient-to-br from-blue-50 to-indigo-100 border border-blue-200 rounded-lg overflow-hidden shadow-sm">
          <img 
            src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=200&h=120&fit=crop" 
            alt="Featured Advertisement" 
            className="w-full h-24 object-cover"
          />
          <div className="p-3">
            <p className="text-sm font-semibold text-blue-800 mb-1">Special Offer</p>
            <p className="text-xs text-blue-600 mb-2">Up to 50% Off on Electronics</p>
            <button className="bg-blue-600 hover:bg-blue-700 text-white text-xs px-3 py-1 rounded transition-colors">
              Shop Now
            </button>
          </div>
        </div>
        
        {/* Newsletter Subscription Card */}
        <div className="bg-gradient-to-br from-red-50 to-orange-100 border border-red-200 rounded-lg p-3 shadow-sm">
          <div className="text-center">
            <div className="w-8 h-8 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-2">
              <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
                <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
              </svg>
            </div>
            <p className="text-sm font-semibold text-red-800 mb-1">समाचार अपडेट</p>
            <p className="text-xs text-red-600 mb-2">ताजा खबरों के लिए सब्सक्राइब करें</p>
            <button className="bg-red-600 hover:bg-red-700 text-white text-xs px-3 py-1 rounded transition-colors">
              सब्सक्राइब करें
            </button>
          </div>
        </div>

        {/* Weather Widget Advertisement */}
        <div className="bg-gradient-to-br from-green-50 to-emerald-100 border border-green-200 rounded-lg p-3 shadow-sm">
          <div className="flex items-center space-x-2 mb-2">
            <div className="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center">
              <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z" clipRule="evenodd"/>
              </svg>
            </div>
            <p className="text-sm font-semibold text-green-800">मौसम अपडेट</p>
          </div>
          <p className="text-xs text-green-600 mb-2">आज का तापमान: 28°C</p>
          <p className="text-xs text-green-500">धूप के साथ हल्की बादल</p>
        </div>

        {/* Social Media Follow Card */}
        <div className="bg-gradient-to-br from-purple-50 to-pink-100 border border-purple-200 rounded-lg p-3 shadow-sm">
          <p className="text-sm font-semibold text-purple-800 mb-2 text-center">हमें फॉलो करें</p>
          <div className="flex justify-center space-x-2">
            <button className="bg-blue-600 hover:bg-blue-700 text-white p-1 rounded transition-colors">
              <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
              </svg>
            </button>
            <button className="bg-blue-800 hover:bg-blue-900 text-white p-1 rounded transition-colors">
              <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
              </svg>
            </button>
            <button className="bg-red-600 hover:bg-red-700 text-white p-1 rounded transition-colors">
              <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
                <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
              </svg>
            </button>
          </div>
        </div>

        {/* Additional Advertisement Space */}
        <AdBanner position="sidebar" maxAds={1} />
      </div>
    </div>
  )
}
