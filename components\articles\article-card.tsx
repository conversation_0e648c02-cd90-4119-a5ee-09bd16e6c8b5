import Link from 'next/link'
import Image from 'next/image'
import { formatDistanceToNow } from 'date-fns'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Clock, User } from 'lucide-react'

interface ArticleCardProps {
  article: {
    id: string
    title: string
    slug: string
    excerpt?: string
    featured_image?: string
    is_featured: boolean
    is_breaking: boolean
    created_at: string
    published_at?: string
    author_name?: string
    author_avatar?: string
    category_name?: string
    category_slug?: string
  }
  variant?: 'default' | 'featured' | 'compact'
  showAuthor?: boolean
  showCategory?: boolean
}

export function ArticleCard({ 
  article, 
  variant = 'default',
  showAuthor = true,
  showCategory = true 
}: ArticleCardProps) {
  const imageUrl = article.featured_image || 'https://images.unsplash.com/photo-1504711434969-e33886168f5c?w=800&h=600&fit=crop&crop=center'
  const publishedDate = article.published_at || article.created_at

  if (variant === 'featured') {
    return (
      <Card className="overflow-hidden group hover:shadow-lg transition-shadow">
        <Link href={`/article/${article.slug}`}>
          <div className="relative h-64 md:h-80">
            <Image
              src={imageUrl}
              alt={article.title}
              fill
              className="object-cover group-hover:scale-105 transition-transform duration-300"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
            
            {/* Badges */}
            <div className="absolute top-4 left-4 flex gap-2">
              {article.is_breaking && (
                <Badge variant="destructive" className="animate-pulse">
                  BREAKING
                </Badge>
              )}
              {article.is_featured && (
                <Badge className="bg-blue-600">
                  FEATURED
                </Badge>
              )}
            </div>

            {/* Content overlay */}
            <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
              {showCategory && article.category_name && (
                <Link 
                  href={`/category/${article.category_slug}`}
                  className="text-blue-400 text-sm font-medium hover:underline"
                >
                  {article.category_name}
                </Link>
              )}
              <h2 className="text-xl md:text-2xl font-bold mt-2 line-clamp-3 group-hover:text-blue-200 transition-colors">
                {article.title}
              </h2>
              {article.excerpt && (
                <p className="text-gray-200 mt-2 line-clamp-2 text-sm">
                  {article.excerpt}
                </p>
              )}
              
              {showAuthor && (
                <div className="flex items-center mt-4 text-sm text-gray-300">
                  <Avatar className="h-6 w-6 mr-2">
                    <AvatarImage src={article.author_avatar || ''} />
                    <AvatarFallback className="text-xs">
                      {article.author_name?.charAt(0) || 'A'}
                    </AvatarFallback>
                  </Avatar>
                  <span className="mr-4">{article.author_name}</span>
                  <Clock className="h-4 w-4 mr-1" />
                  <span>{formatDistanceToNow(new Date(publishedDate), { addSuffix: true })}</span>
                </div>
              )}
            </div>
          </div>
        </Link>
      </Card>
    )
  }

  if (variant === 'compact') {
    return (
      <Card className="overflow-hidden group hover:shadow-md transition-shadow">
        <Link href={`/article/${article.slug}`}>
          <div className="flex">
            <div className="relative w-24 h-24 flex-shrink-0">
              <Image
                src={imageUrl}
                alt={article.title}
                fill
                className="object-cover"
              />
            </div>
            <CardContent className="flex-1 p-4">
              <div className="flex gap-2 mb-2">
                {article.is_breaking && (
                  <Badge variant="destructive" className="text-xs">
                    BREAKING
                  </Badge>
                )}
                {showCategory && article.category_name && (
                  <Badge variant="outline" className="text-xs">
                    {article.category_name}
                  </Badge>
                )}
              </div>
              <h3 className="font-semibold text-sm line-clamp-2 group-hover:text-blue-600 transition-colors">
                {article.title}
              </h3>
              <div className="flex items-center mt-2 text-xs text-gray-500">
                <Clock className="h-3 w-3 mr-1" />
                <span>{formatDistanceToNow(new Date(publishedDate), { addSuffix: true })}</span>
              </div>
            </CardContent>
          </div>
        </Link>
      </Card>
    )
  }

  // Default variant
  return (
    <Card className="overflow-hidden group hover:shadow-lg transition-shadow">
      <Link href={`/article/${article.slug}`}>
        <div className="relative h-48">
          <Image
            src={imageUrl}
            alt={article.title}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
          />
          <div className="absolute top-4 left-4 flex gap-2">
            {article.is_breaking && (
              <Badge variant="destructive" className="animate-pulse">
                BREAKING
              </Badge>
            )}
            {article.is_featured && (
              <Badge className="bg-blue-600">
                FEATURED
              </Badge>
            )}
          </div>
        </div>
        
        <CardContent className="p-4">
          {showCategory && article.category_name && (
            <Link 
              href={`/category/${article.category_slug}`}
              className="text-blue-600 text-sm font-medium hover:underline"
            >
              {article.category_name}
            </Link>
          )}
          
          <h3 className="font-bold text-lg mt-2 line-clamp-2 group-hover:text-blue-600 transition-colors">
            {article.title}
          </h3>
          
          {article.excerpt && (
            <p className="text-gray-600 mt-2 line-clamp-3 text-sm">
              {article.excerpt}
            </p>
          )}
          
          {showAuthor && (
            <div className="flex items-center justify-between mt-4 text-sm text-gray-500">
              <div className="flex items-center">
                <Avatar className="h-6 w-6 mr-2">
                  <AvatarImage src={article.author_avatar || ''} />
                  <AvatarFallback className="text-xs">
                    {article.author_name?.charAt(0) || 'A'}
                  </AvatarFallback>
                </Avatar>
                <span>{article.author_name}</span>
              </div>
              <div className="flex items-center">
                <Clock className="h-4 w-4 mr-1" />
                <span>{formatDistanceToNow(new Date(publishedDate), { addSuffix: true })}</span>
              </div>
            </div>
          )}
        </CardContent>
      </Link>
    </Card>
  )
}
