'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { db } from '@/lib/database'
import { 
  FileText, 
  Users, 
  Eye, 
  TrendingUp, 
  Calendar,
  Star,
  AlertCircle
} from 'lucide-react'

interface AnalyticsData {
  totalArticles: number
  publishedArticles: number
  draftArticles: number
  featuredArticles: number
  breakingNews: number
  totalUsers: number
  totalSubscribers: number
  recentActivity: {
    articlesThisWeek: number
    articlesThisMonth: number
    newUsersThisWeek: number
  }
}

export function AnalyticsOverview() {
  const [analytics, setAnalytics] = useState<AnalyticsData>({
    totalArticles: 0,
    publishedArticles: 0,
    draftArticles: 0,
    featuredArticles: 0,
    breakingNews: 0,
    totalUsers: 0,
    totalSubscribers: 0,
    recentActivity: {
      articlesThisWeek: 0,
      articlesThisMonth: 0,
      newUsersThisWeek: 0,
    }
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchAnalytics()
  }, [])

  const fetchAnalytics = async () => {
    try {
      // Fetch articles data
      const allArticles = await db.getPublishedArticles({ limit: 1000 })
      const users = await db.getUsers()
      const subscribers = await db.getNewsletterSubscribers()

      // Calculate analytics
      const publishedArticles = allArticles.filter(a => a.is_published).length
      const draftArticles = allArticles.length - publishedArticles
      const featuredArticles = allArticles.filter(a => a.is_featured).length
      const breakingNews = allArticles.filter(a => a.is_breaking).length

      // Calculate recent activity (simplified - in real app you'd have proper date filtering)
      const oneWeekAgo = new Date()
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)
      
      const oneMonthAgo = new Date()
      oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1)

      const articlesThisWeek = allArticles.filter(
        a => new Date(a.created_at) > oneWeekAgo
      ).length

      const articlesThisMonth = allArticles.filter(
        a => new Date(a.created_at) > oneMonthAgo
      ).length

      const newUsersThisWeek = users.filter(
        u => new Date(u.created_at) > oneWeekAgo
      ).length

      setAnalytics({
        totalArticles: allArticles.length,
        publishedArticles,
        draftArticles,
        featuredArticles,
        breakingNews,
        totalUsers: users.length,
        totalSubscribers: subscribers.length,
        recentActivity: {
          articlesThisWeek,
          articlesThisMonth,
          newUsersThisWeek,
        }
      })
    } catch (error) {
      console.error('Error fetching analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  const statCards = [
    {
      title: 'Total Articles',
      value: analytics.totalArticles,
      description: `${analytics.publishedArticles} published, ${analytics.draftArticles} drafts`,
      icon: FileText,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      title: 'Featured Articles',
      value: analytics.featuredArticles,
      description: 'Currently featured on homepage',
      icon: Star,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
    },
    {
      title: 'Breaking News',
      value: analytics.breakingNews,
      description: 'Active breaking news stories',
      icon: AlertCircle,
      color: 'text-red-600',
      bgColor: 'bg-red-100',
    },
    {
      title: 'Total Users',
      value: analytics.totalUsers,
      description: `${analytics.recentActivity.newUsersThisWeek} new this week`,
      icon: Users,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      title: 'Newsletter Subscribers',
      value: analytics.totalSubscribers,
      description: 'Active email subscribers',
      icon: TrendingUp,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
    },
    {
      title: 'This Month',
      value: analytics.recentActivity.articlesThisMonth,
      description: 'Articles published this month',
      icon: Calendar,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-100',
    },
  ]

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="h-20 bg-gray-200 animate-pulse rounded" />
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {statCards.map((stat, index) => {
          const Icon = stat.icon
          return (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                    <p className="text-xs text-gray-500 mt-1">{stat.description}</p>
                  </div>
                  <div className={`p-3 rounded-full ${stat.bgColor}`}>
                    <Icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
          <CardDescription>
            Overview of recent content and user activity
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {analytics.recentActivity.articlesThisWeek}
              </div>
              <div className="text-sm text-gray-600">Articles This Week</div>
            </div>
            
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {analytics.recentActivity.articlesThisMonth}
              </div>
              <div className="text-sm text-gray-600">Articles This Month</div>
            </div>
            
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {analytics.recentActivity.newUsersThisWeek}
              </div>
              <div className="text-sm text-gray-600">New Users This Week</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
