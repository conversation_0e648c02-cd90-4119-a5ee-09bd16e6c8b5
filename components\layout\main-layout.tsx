import { Navbar } from './navbar'
import { Footer } from './footer'
import { BreakingNewsTicker } from './breaking-news-ticker'

interface MainLayoutProps {
  children: React.ReactNode
  showBreakingNews?: boolean
}

export function MainLayout({ children, showBreakingNews = true }: MainLayoutProps) {
  return (
    <div className="min-h-screen flex flex-col">
      {showBreakingNews && <BreakingNewsTicker />}
      <Navbar />
      <main className="flex-1">
        {children}
      </main>
      <Footer />
    </div>
  )
}
