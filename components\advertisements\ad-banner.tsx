'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { DatabaseClient } from '@/lib/database'
import { X } from 'lucide-react'

interface Advertisement {
  id: string
  title: string
  description: string
  image_url: string
  link_url?: string
  position: string
  is_active: boolean
}

interface AdBannerProps {
  position: 'header' | 'sidebar' | 'content' | 'footer' | 'mobile'
  className?: string
  maxAds?: number
}

export function AdBanner({ position, className = '', maxAds = 1 }: AdBannerProps) {
  const [advertisements, setAdvertisements] = useState<Advertisement[]>([])
  const [loading, setLoading] = useState(true)
  const [closedAds, setClosedAds] = useState<Set<string>>(new Set())

  const db = new DatabaseClient()

  useEffect(() => {
    fetchAdvertisements()
  }, [position])

  const fetchAdvertisements = async () => {
    try {
      setLoading(true)
      const ads = await db.getActiveAdvertisements(position)
      setAdvertisements(ads.slice(0, maxAds))
    } catch (error) {
      console.error('Error fetching advertisements:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCloseAd = (adId: string) => {
    setClosedAds(prev => new Set([...prev, adId]))
  }

  const visibleAds = advertisements.filter(ad => !closedAds.has(ad.id))

  if (loading || visibleAds.length === 0) {
    return null
  }

  const getAdStyles = () => {
    switch (position) {
      case 'header':
        return 'w-full h-24 md:h-32 bg-gradient-to-r from-blue-50 to-purple-50 border border-gray-200 rounded-lg overflow-hidden'
      case 'sidebar':
        return 'w-full h-64 bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm'
      case 'content':
        return 'w-full h-40 md:h-48 bg-gradient-to-r from-green-50 to-blue-50 border border-gray-200 rounded-lg overflow-hidden my-6'
      case 'footer':
        return 'w-full h-20 md:h-24 bg-gray-100 border border-gray-200 rounded-lg overflow-hidden'
      case 'mobile':
        return 'w-full h-32 bg-gradient-to-r from-orange-50 to-red-50 border border-gray-200 rounded-lg overflow-hidden'
      default:
        return 'w-full h-32 bg-white border border-gray-200 rounded-lg overflow-hidden'
    }
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {visibleAds.map((ad) => (
        <div key={ad.id} className={`relative group ${getAdStyles()}`}>
          {/* Close button for sidebar and content ads */}
          {(position === 'sidebar' || position === 'content') && (
            <button
              onClick={() => handleCloseAd(ad.id)}
              className="absolute top-2 right-2 z-10 bg-white/80 hover:bg-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
            >
              <X className="w-4 h-4 text-gray-600" />
            </button>
          )}

          {ad.link_url ? (
            <Link href={ad.link_url} target="_blank" rel="noopener noreferrer">
              <AdContent ad={ad} position={position} />
            </Link>
          ) : (
            <AdContent ad={ad} position={position} />
          )}

          {/* Advertisement label */}
          <div className="absolute top-2 left-2 bg-gray-800/80 text-white text-xs px-2 py-1 rounded">
            विज्ञापन
          </div>
        </div>
      ))}
    </div>
  )
}

interface AdContentProps {
  ad: Advertisement
  position: string
}

function AdContent({ ad, position }: AdContentProps) {
  if (position === 'header' || position === 'footer' || position === 'mobile') {
    return (
      <div className="flex items-center h-full p-4">
        <div className="flex-shrink-0 w-16 h-16 md:w-20 md:h-20 relative">
          <Image
            src={ad.image_url}
            alt={ad.title}
            fill
            className="object-cover rounded"
          />
        </div>
        <div className="ml-4 flex-1 min-w-0">
          <h3 className="text-sm md:text-base font-semibold text-gray-900 truncate">
            {ad.title}
          </h3>
          <p className="text-xs md:text-sm text-gray-600 line-clamp-2">
            {ad.description}
          </p>
        </div>
      </div>
    )
  }

  if (position === 'sidebar') {
    return (
      <div className="h-full">
        <div className="relative h-2/3">
          <Image
            src={ad.image_url}
            alt={ad.title}
            fill
            className="object-cover"
          />
        </div>
        <div className="p-3 h-1/3 flex flex-col justify-center">
          <h3 className="font-semibold text-sm text-gray-900 line-clamp-1">
            {ad.title}
          </h3>
          <p className="text-xs text-gray-600 line-clamp-2 mt-1">
            {ad.description}
          </p>
        </div>
      </div>
    )
  }

  // Content position
  return (
    <div className="flex items-center h-full p-4 md:p-6">
      <div className="flex-shrink-0 w-24 h-24 md:w-32 md:h-32 relative">
        <Image
          src={ad.image_url}
          alt={ad.title}
          fill
          className="object-cover rounded-lg"
        />
      </div>
      <div className="ml-4 md:ml-6 flex-1 min-w-0">
        <h3 className="text-lg md:text-xl font-bold text-gray-900 mb-2">
          {ad.title}
        </h3>
        <p className="text-sm md:text-base text-gray-600 line-clamp-3">
          {ad.description}
        </p>
        {ad.link_url && (
          <div className="mt-3">
            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              और जानें →
            </span>
          </div>
        )}
      </div>
    </div>
  )
}
