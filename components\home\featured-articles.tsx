import { dbServer } from '@/lib/database-server'
import { ArticleCard } from '@/components/articles/article-card'

export async function FeaturedArticles() {
  try {
    const featuredArticles = await dbServer.getPublishedArticles({
      limit: 4,
      isFeatured: true
    })

    if (featuredArticles.length === 0) {
      return (
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Featured Articles</h2>
          <p className="text-gray-600">No featured articles available at the moment.</p>
        </div>
      )
    }

    const mainFeatured = featuredArticles[0]
    const secondaryFeatured = featuredArticles.slice(1, 4)

    return (
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Featured Article */}
        <div className="lg:col-span-2">
          <ArticleCard 
            article={mainFeatured} 
            variant="featured"
            showAuthor={true}
            showCategory={true}
          />
        </div>

        {/* Secondary Featured Articles */}
        <div className="space-y-4">
          <h3 className="text-xl font-bold text-gray-900 mb-4">More Featured</h3>
          {secondaryFeatured.map((article) => (
            <ArticleCard
              key={article.id}
              article={article}
              variant="compact"
              showAuthor={false}
              showCategory={true}
            />
          ))}
        </div>
      </div>
    )
  } catch (error) {
    console.error('Error fetching featured articles:', error)
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Featured Articles</h2>
        <p className="text-red-600">Error loading featured articles. Please try again later.</p>
      </div>
    )
  }
}
