'use client'

import { useState, useEffect } from 'react'
import { DatabaseClient } from '@/lib/database'
import { TraditionalHeroSection } from './traditional-hero-section'
import { TraditionalMainContent } from './traditional-main-content'
import { TraditionalSidebar } from './traditional-sidebar'
import { TraditionalBottomSection } from './traditional-bottom-section'
import { AdBanner } from '@/components/advertisements/ad-banner'
import { LeftAdSidebar } from './left-ad-sidebar'

interface Article {
  id: string
  title: string
  excerpt: string
  image_url?: string
  published_at: string
  category: string
  slug: string
}

interface Category {
  id: string
  name: string
  slug: string
}

export function TraditionalHomepage() {
  const [data, setData] = useState<{
    featuredArticles: Article[]
    latestArticles: Article[]
    breakingNews: Article[]
    categories: Category[]
  } | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchData() {
      try {
        const db = new DatabaseClient()

        // Fetch all the data we need
        const [featuredArticles, latestArticles, breakingNews, categories] = await Promise.all([
          db.getPublishedArticles({ limit: 6, isFeatured: true }),
          db.getPublishedArticles({ limit: 20 }),
          db.getPublishedArticles({ limit: 5, isBreaking: true }),
          db.getCategories()
        ])

        setData({
          featuredArticles,
          latestArticles,
          breakingNews,
          categories
        })
      } catch (err) {
        console.error('Error loading homepage:', err)
        setError('Error loading news')
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  if (loading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Loading News...</h2>
          <p className="text-gray-600">Please wait</p>
        </div>
      </div>
    )
  }

  if (error || !data) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">Error</h2>
          <p className="text-gray-600">{error || 'Failed to load data'}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white min-h-screen">
      {/* Header Advertisement */}
      <div className="max-w-7xl mx-auto px-2 py-2">
        <AdBanner position="header" />
      </div>

      {/* Hero Section with Main Story */}
      <TraditionalHeroSection
        mainStory={data.featuredArticles[0]}
        featuredStories={data.featuredArticles.slice(1, 4)}
        breakingNews={data.breakingNews}
      />

      {/* Mobile Advertisement Section */}
      <div className="lg:hidden max-w-7xl mx-auto px-2 py-2">
        <div className="grid grid-cols-2 gap-2">
          <AdBanner position="mobile" maxAds={2} />
        </div>
      </div>

      {/* Main Content Area with Left Advertisement Sidebar */}
      <div className="max-w-7xl mx-auto px-2 py-4">
        <div className="grid grid-cols-12 gap-4">
          {/* Left Advertisement Sidebar - 2 columns */}
          <div className="col-span-12 lg:col-span-2 hidden lg:block">
            <LeftAdSidebar />
          </div>

          {/* Main Content - 7 columns */}
          <div className="col-span-12 lg:col-span-7">
            <TraditionalMainContent
              articles={data.latestArticles}
              categories={data.categories}
            />

            {/* Content Advertisement */}
            <AdBanner position="content" className="my-6" />
          </div>

          {/* Right Sidebar - 3 columns */}
          <div className="col-span-12 lg:col-span-3">
            <TraditionalSidebar
              trendingArticles={data.latestArticles.slice(0, 8)}
              categories={data.categories}
            />

            {/* Right Sidebar Advertisements */}
            <div className="mt-6">
              <AdBanner position="sidebar" maxAds={2} />
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Section */}
      <TraditionalBottomSection
        articles={data.latestArticles.slice(10, 20)}
        categories={data.categories}
      />

      {/* Footer Advertisement */}
      <div className="max-w-7xl mx-auto px-2 py-2">
        <AdBanner position="footer" />
      </div>
    </div>
  )
}
