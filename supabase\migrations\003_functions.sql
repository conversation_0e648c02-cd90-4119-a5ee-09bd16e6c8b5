-- Function to handle new user signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name, role)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
    'guest'
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to automatically create profile on signup
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to get user role
CREATE OR REPLACE FUNCTION public.get_user_role(user_id UUID)
RETURNS user_role AS $$
BEGIN
  RETURN (
    SELECT role FROM profiles WHERE id = user_id
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user has permission
CREATE OR REPLACE FUNCTION public.has_permission(
  user_id UUID,
  required_roles user_role[]
)
<PERSON><PERSON>URNS BOOLEAN AS $$
BEGIN
  RETURN (
    SELECT role = ANY(required_roles)
    FROM profiles 
    WHERE id = user_id
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get published articles with author and category info
CREATE OR REPLACE FUNCTION public.get_published_articles(
  limit_count INTEGER DEFAULT 10,
  offset_count INTEGER DEFAULT 0,
  category_slug_param TEXT DEFAULT NULL,
  is_featured_only BOOLEAN DEFAULT FALSE,
  is_breaking_only BOOLEAN DEFAULT FALSE
)
RETURNS TABLE (
  id UUID,
  title TEXT,
  slug TEXT,
  content TEXT,
  excerpt TEXT,
  featured_image TEXT,
  is_featured BOOLEAN,
  is_breaking BOOLEAN,
  created_at TIMESTAMP WITH TIME ZONE,
  published_at TIMESTAMP WITH TIME ZONE,
  author_name TEXT,
  author_avatar TEXT,
  category_name TEXT,
  category_slug TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    a.id,
    a.title,
    a.slug,
    a.content,
    a.excerpt,
    a.featured_image,
    a.is_featured,
    a.is_breaking,
    a.created_at,
    a.published_at,
    p.full_name as author_name,
    p.avatar_url as author_avatar,
    c.name as category_name,
    c.slug as category_slug
  FROM articles a
  JOIN profiles p ON a.author_id = p.id
  JOIN categories c ON a.category_id = c.id
  WHERE
    a.is_published = true
    AND (category_slug_param IS NULL OR c.slug = category_slug_param)
    AND (is_featured_only = false OR a.is_featured = true)
    AND (is_breaking_only = false OR a.is_breaking = true)
  ORDER BY 
    CASE WHEN is_breaking_only THEN a.is_breaking END DESC,
    CASE WHEN is_featured_only THEN a.is_featured END DESC,
    a.published_at DESC NULLS LAST,
    a.created_at DESC
  LIMIT limit_count
  OFFSET offset_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get article by slug with full details
CREATE OR REPLACE FUNCTION public.get_article_by_slug(article_slug TEXT)
RETURNS TABLE (
  id UUID,
  title TEXT,
  slug TEXT,
  content TEXT,
  excerpt TEXT,
  featured_image TEXT,
  is_featured BOOLEAN,
  is_breaking BOOLEAN,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE,
  published_at TIMESTAMP WITH TIME ZONE,
  author_id UUID,
  author_name TEXT,
  author_avatar TEXT,
  category_id UUID,
  category_name TEXT,
  category_slug TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    a.id,
    a.title,
    a.slug,
    a.content,
    a.excerpt,
    a.featured_image,
    a.is_featured,
    a.is_breaking,
    a.created_at,
    a.updated_at,
    a.published_at,
    a.author_id,
    p.full_name as author_name,
    p.avatar_url as author_avatar,
    a.category_id,
    c.name as category_name,
    c.slug as category_slug
  FROM articles a
  JOIN profiles p ON a.author_id = p.id
  JOIN categories c ON a.category_id = c.id
  WHERE 
    a.slug = article_slug
    AND (a.is_published = true OR a.author_id = auth.uid());
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to search articles
CREATE OR REPLACE FUNCTION public.search_articles(
  search_query TEXT,
  limit_count INTEGER DEFAULT 10,
  offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
  id UUID,
  title TEXT,
  slug TEXT,
  content TEXT,
  excerpt TEXT,
  featured_image TEXT,
  is_featured BOOLEAN,
  is_breaking BOOLEAN,
  created_at TIMESTAMP WITH TIME ZONE,
  published_at TIMESTAMP WITH TIME ZONE,
  author_name TEXT,
  author_avatar TEXT,
  category_name TEXT,
  category_slug TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    a.id,
    a.title,
    a.slug,
    a.content,
    a.excerpt,
    a.featured_image,
    a.is_featured,
    a.is_breaking,
    a.created_at,
    a.published_at,
    p.full_name as author_name,
    p.avatar_url as author_avatar,
    c.name as category_name,
    c.slug as category_slug
  FROM articles a
  JOIN profiles p ON a.author_id = p.id
  JOIN categories c ON a.category_id = c.id
  WHERE
    a.is_published = true
    AND (
      a.title ILIKE '%' || search_query || '%'
      OR a.content ILIKE '%' || search_query || '%'
      OR a.excerpt ILIKE '%' || search_query || '%'
    )
  ORDER BY
    a.published_at DESC NULLS LAST,
    a.created_at DESC
  LIMIT limit_count
  OFFSET offset_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
