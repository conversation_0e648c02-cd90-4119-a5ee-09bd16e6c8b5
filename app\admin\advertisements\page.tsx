'use client'

import { useState, useEffect } from 'react'
import { DatabaseClient } from '@/lib/database'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Trash2, Edit, Plus, Eye } from 'lucide-react'
import Image from 'next/image'

interface Advertisement {
  id: string
  title: string
  description: string
  image_url: string
  link_url?: string
  position: string
  is_active: boolean
  start_date: string
  end_date?: string
  created_at: string
}

export default function AdvertisementsPage() {
  const [advertisements, setAdvertisements] = useState<Advertisement[]>([])
  const [loading, setLoading] = useState(true)
  const [showForm, setShowForm] = useState(false)
  const [editingAd, setEditingAd] = useState<Advertisement | null>(null)
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    image_url: '',
    link_url: '',
    position: 'sidebar',
    is_active: true,
    start_date: new Date().toISOString().split('T')[0],
    end_date: ''
  })

  const db = new DatabaseClient()

  useEffect(() => {
    fetchAdvertisements()
  }, [])

  const fetchAdvertisements = async () => {
    try {
      setLoading(true)
      const ads = await db.getAdvertisements()
      setAdvertisements(ads)
    } catch (error) {
      console.error('Error fetching advertisements:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      if (editingAd) {
        await db.updateAdvertisement(editingAd.id, formData)
      } else {
        await db.createAdvertisement(formData)
      }
      
      setShowForm(false)
      setEditingAd(null)
      resetForm()
      fetchAdvertisements()
    } catch (error) {
      console.error('Error saving advertisement:', error)
    }
  }

  const handleEdit = (ad: Advertisement) => {
    setEditingAd(ad)
    setFormData({
      title: ad.title,
      description: ad.description,
      image_url: ad.image_url,
      link_url: ad.link_url || '',
      position: ad.position,
      is_active: ad.is_active,
      start_date: ad.start_date.split('T')[0],
      end_date: ad.end_date ? ad.end_date.split('T')[0] : ''
    })
    setShowForm(true)
  }

  const handleDelete = async (id: string) => {
    if (confirm('क्या आप वाकई इस विज्ञापन को डिलीट करना चाहते हैं?')) {
      try {
        await db.deleteAdvertisement(id)
        fetchAdvertisements()
      } catch (error) {
        console.error('Error deleting advertisement:', error)
      }
    }
  }

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      image_url: '',
      link_url: '',
      position: 'sidebar',
      is_active: true,
      start_date: new Date().toISOString().split('T')[0],
      end_date: ''
    })
  }

  const getPositionLabel = (position: string) => {
    const labels = {
      header: 'हेडर',
      sidebar: 'साइडबार',
      content: 'कंटेंट',
      footer: 'फुटर'
    }
    return labels[position as keyof typeof labels] || position
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">विज्ञापन प्रबंधन</h1>
          <p className="text-gray-600 mt-2">वेबसाइट पर दिखाए जाने वाले विज्ञापनों को प्रबंधित करें</p>
        </div>
        <Button 
          onClick={() => {
            setShowForm(true)
            setEditingAd(null)
            resetForm()
          }}
          className="bg-blue-600 hover:bg-blue-700"
        >
          <Plus className="w-4 h-4 mr-2" />
          नया विज्ञापन जोड़ें
        </Button>
      </div>

      {showForm && (
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>
              {editingAd ? 'विज्ञापन संपादित करें' : 'नया विज्ञापन जोड़ें'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">शीर्षक</label>
                  <Input
                    value={formData.title}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">स्थिति</label>
                  <Select value={formData.position} onValueChange={(value) => setFormData({ ...formData, position: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="header">हेडर</SelectItem>
                      <SelectItem value="sidebar">साइडबार</SelectItem>
                      <SelectItem value="content">कंटेंट</SelectItem>
                      <SelectItem value="footer">फुटर</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">विवरण</label>
                <Textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">इमेज URL</label>
                  <Input
                    value={formData.image_url}
                    onChange={(e) => setFormData({ ...formData, image_url: e.target.value })}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">लिंक URL</label>
                  <Input
                    value={formData.link_url}
                    onChange={(e) => setFormData({ ...formData, link_url: e.target.value })}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">शुरुआती तारीख</label>
                  <Input
                    type="date"
                    value={formData.start_date}
                    onChange={(e) => setFormData({ ...formData, start_date: e.target.value })}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">समाप्ति तारीख</label>
                  <Input
                    type="date"
                    value={formData.end_date}
                    onChange={(e) => setFormData({ ...formData, end_date: e.target.value })}
                  />
                </div>
                <div className="flex items-center space-x-2 mt-6">
                  <input
                    type="checkbox"
                    id="is_active"
                    checked={formData.is_active}
                    onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                    className="rounded"
                  />
                  <label htmlFor="is_active" className="text-sm font-medium">सक्रिय</label>
                </div>
              </div>

              <div className="flex space-x-4">
                <Button type="submit" className="bg-green-600 hover:bg-green-700">
                  {editingAd ? 'अपडेट करें' : 'सेव करें'}
                </Button>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => {
                    setShowForm(false)
                    setEditingAd(null)
                    resetForm()
                  }}
                >
                  रद्द करें
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {advertisements.map((ad) => (
          <Card key={ad.id} className="overflow-hidden">
            <div className="relative h-48">
              <Image
                src={ad.image_url}
                alt={ad.title}
                fill
                className="object-cover"
              />
              <div className="absolute top-2 right-2">
                <Badge variant={ad.is_active ? "default" : "secondary"}>
                  {ad.is_active ? 'सक्रिय' : 'निष्क्रिय'}
                </Badge>
              </div>
            </div>
            <CardContent className="p-4">
              <div className="flex justify-between items-start mb-2">
                <h3 className="font-semibold text-lg line-clamp-1">{ad.title}</h3>
                <Badge variant="outline">{getPositionLabel(ad.position)}</Badge>
              </div>
              <p className="text-gray-600 text-sm mb-3 line-clamp-2">{ad.description}</p>
              
              <div className="text-xs text-gray-500 mb-3">
                <p>शुरुआत: {new Date(ad.start_date).toLocaleDateString('hi-IN')}</p>
                {ad.end_date && (
                  <p>समाप्ति: {new Date(ad.end_date).toLocaleDateString('hi-IN')}</p>
                )}
              </div>

              <div className="flex space-x-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleEdit(ad)}
                >
                  <Edit className="w-4 h-4" />
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleDelete(ad.id)}
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
                {ad.link_url && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => window.open(ad.link_url, '_blank')}
                  >
                    <Eye className="w-4 h-4" />
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {advertisements.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">कोई विज्ञापन नहीं मिला</p>
          <Button 
            onClick={() => setShowForm(true)}
            className="mt-4 bg-blue-600 hover:bg-blue-700"
          >
            पहला विज्ञापन जोड़ें
          </Button>
        </div>
      )}
    </div>
  )
}
