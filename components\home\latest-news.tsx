import { dbServer } from '@/lib/database-server'
import { ArticleCard } from '@/components/articles/article-card'

export async function LatestNews() {
  try {
    const latestArticles = await dbServer.getPublishedArticles({
      limit: 9,
      offset: 0
    })

    if (latestArticles.length === 0) {
      return (
        <div className="text-center py-12">
          <p className="text-gray-600">No articles available at the moment.</p>
        </div>
      )
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {latestArticles.map((article) => (
          <ArticleCard
            key={article.id}
            article={article}
            variant="default"
            showAuthor={true}
            showCategory={true}
          />
        ))}
      </div>
    )
  } catch (error) {
    console.error('Error fetching latest articles:', error)
    return (
      <div className="text-center py-12">
        <p className="text-red-600">Error loading articles. Please try again later.</p>
      </div>
    )
  }
}
