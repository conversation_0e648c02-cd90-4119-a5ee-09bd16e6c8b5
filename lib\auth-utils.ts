import { User, UserRole } from './types'

/**
 * Determines the appropriate redirect path after successful authentication
 * based on user role and permissions
 */
export function getRedirectPath(user: User | null): string {
  if (!user) {
    return '/'
  }

  // Admin, Editor, and Writer roles can access admin dashboard
  if (['admin', 'editor', 'writer'].includes(user.role)) {
    return '/admin'
  }

  // Guest users go to homepage
  return '/'
}

/**
 * Checks if a user has permission to access admin areas
 */
export function canAccessAdmin(user: User | null): boolean {
  if (!user) return false
  return ['admin', 'editor', 'writer'].includes(user.role)
}

/**
 * Checks if a user has specific role
 */
export function hasRole(user: User | null, role: UserRole): boolean {
  return user?.role === role
}

/**
 * Checks if a user has any of the specified roles
 */
export function hasAnyRole(user: User | null, roles: UserRole[]): boolean {
  if (!user) return false
  return roles.includes(user.role)
}

/**
 * Gets user-friendly role display name
 */
export function getRoleDisplayName(role: UserRole): string {
  const roleNames: Record<UserRole, string> = {
    admin: 'Administrator',
    editor: 'Editor',
    writer: 'Writer',
    guest: 'Guest'
  }
  return roleNames[role] || 'Unknown'
}

/**
 * Gets role-based welcome message
 */
export function getWelcomeMessage(user: User): string {
  const roleMessages: Record<UserRole, string> = {
    admin: 'Welcome to the admin dashboard! You have full system access.',
    editor: 'Welcome! You can manage and publish articles.',
    writer: 'Welcome! You can create and edit your articles.',
    guest: 'Welcome to our news portal!'
  }
  return roleMessages[user.role] || 'Welcome!'
}
